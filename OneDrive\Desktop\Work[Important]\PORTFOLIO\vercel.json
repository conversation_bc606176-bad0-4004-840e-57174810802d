{"version": 2, "name": "pratyush-portfolio", "builds": [{"src": "**/*", "use": "@vercel/static"}], "routes": [{"src": "/", "dest": "/home.html"}, {"src": "/home", "dest": "/home.html"}, {"src": "/new", "dest": "/new.html"}, {"src": "/(.*\\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot))", "dest": "/$1", "headers": {"Cache-Control": "public, max-age=31536000, immutable"}}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}, {"source": "/**/*.html", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}], "cleanUrls": true, "trailingSlash": false, "functions": {}, "regions": ["iad1"], "github": {"silent": true}}