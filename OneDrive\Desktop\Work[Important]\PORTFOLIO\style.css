body {
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', sans-serif;
  background: white; /* Background color to prevent white flash */
  overflow-x: hidden; /* Allow horizontal scrolling */
}

/* ===== WELCOME SCREEN STYLES ===== */
.welcome-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  opacity: 1;
  transition: opacity 1s ease-in-out;
}

.welcome-screen.fade-out {
  opacity: 0;
  pointer-events: none;
}

.welcome-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, #1e1f26, #6610f2, #3ec9f3);
}

/* Three.js Canvas */
#three-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* Particles Background */
#particles-js {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

/* Welcome Text Container */
.welcome-content {
  position: absolute;
  z-index: 10;
  text-align: center;
  color: white;
  opacity: 0;
  transform: translateY(50px) scale(0.8);
}

.welcome-title {
  font-family: 'Orbitron', monospace;
  font-size: 4rem;
  font-weight: 700;
  color: #f7f7f7;
  margin-bottom: 20px;
  letter-spacing: 3px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.welcome-subtitle {
  font-family: 'Rajdhani', sans-serif;
  font-size: 1.5rem;
  font-weight: 400;
  color: #ffffff;
  margin-bottom: 30px;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.tech-showcase {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
  flex-wrap: wrap;
}

.tech-item {
  padding: 8px 16px;
  border: 1px solid #e5e5e5;
  border-radius: 20px;
  font-size: 0.9rem;
  color: #000000;
  background: rgba(102, 16, 242, 0.1);
  backdrop-filter: blur(10px);
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.3s ease;
}

.tech-item:hover {
  border-color: #6610f2;
  background: rgba(102, 16, 242, 0.2);
  transform: translateY(0px) scale(1.05);
}

/* Welcome Screen Animations */
@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* Loading Animation */
.loading-container {
  position: absolute;
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 11;
  text-align: center;
}

.loading-text {
  color: #e5e5e5;
  font-size: 1rem;
  margin-bottom: 20px;
  animation: pulse 2s infinite;
}

.loading-bar {
  width: 200px;
  height: 3px;
  background: rgba(229, 229, 229, 0.3);
  border-radius: 2px;
  overflow: hidden;
}

.loading-progress {
  height: 100%;
  background: linear-gradient(90deg, #6610f2, #3ec9f3);
  width: 0%;
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* Main Content */
.main-content {
  opacity: 0;
  transform: translateY(20px);
  transition: all 1s ease-in-out;
  background: #f8f9fa; /* Background for main content */
  min-height: 100vh;
}

.main-content.show {
  opacity: 1;
  transform: translateY(0);
}

/* Welcome Screen Responsive Design */
@media (max-width: 768px) {
  .welcome-title {
    font-size: 2.5rem;
  }
  .welcome-subtitle {
    font-size: 1.2rem;
  }
  .tech-showcase {
    gap: 10px;
  }
  .tech-item {
    font-size: 0.8rem;
    padding: 6px 12px;
  }
}

@media (max-width: 480px) {
  .welcome-title {
    font-size: 2rem;
    letter-spacing: 1px;
  }
  .welcome-subtitle {
    font-size: 1rem;
  }
}

/* ===== END WELCOME SCREEN STYLES ===== */
body {
            background-color: black;
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
            margin: 0;
            font-family: system-ui, -apple-system, sans-serif;
        }

        /* Layout */
        .flex {
            display: flex;
        }
        
        .items-center {
            align-items: center;
        }
        
        .justify-center {
            justify-content: center;
        }
        
        .space-x-4 > * + * {
            margin-left: 1rem;
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        /* Navigation */
        .nav-container {
            position: fixed;
            top: 1.5rem;
            left: 50%;
            transform: translateX(-50%);
            z-index: 50;
            display: flex;
            align-items: center;
            gap: 2rem;
        }
        .nav-menu {
            position: relative;
            display: flex;
            align-items: center;
            gap: 1rem;
            background: rgba(255,255,255,0.08);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.18);
            border-radius: 9999px;
            padding: 0.75rem 1.5rem;
            box-shadow: 0 4px 16px 0 rgba(0,0,0,0.12);
        }
        .nav-menu a {
            text-decoration: none;
            border: none;
            color: white;
            background: transparent;
            position: relative;
            z-index: 2;
            border-radius: 9999px;
            padding: 0.5rem 1.25rem;
            font-weight: 500;
            font-size: 1rem;
            transition: color 0.2s, background 0.2s, box-shadow 0.2s;
        }
        .nav-menu a:hover {
            background: rgba(255,255,255,0.18);
            color: black;
            box-shadow: 0 2px 8px 0 rgba(0,0,0,0.10);
        }
        .nav-menu a.active {
            color: black;
            background: white;
        }
        .subpill-indicator {
            position: absolute;
            top: 50%;
            left: 0;
            width: 0;
            height: 40px;
            background: rgba(255,255,255,0.25);
            border-radius: 9999px;
            transform: translateY(-50%);
            z-index: 1;
            transition: all 0.3s cubic-bezier(.4,0,.2,1);
            pointer-events: none;
            box-shadow: 0 2px 8px 0 rgba(0,0,0,0.10);
        }
        /* Hero Section */
        .hero-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 1rem;
            position: relative;
        }

        .spline-logo-hider{
          position: absolute;
          bottom: 16px;
          right: 16px;
          width: 150px;
          height: 48px;
          background: #000000;
          border-radius: 16px;
          z-index: 100;
          pointer-events: none;
        }
        
        .hero-background {
            position: absolute;
            inset: 0;
            overflow: hidden;
        }
        
        .hero-blob {
            position: absolute;
            border-radius: 50%;
            background: white;
            opacity: 0.05;
            filter: blur(48px);
        }
        
        .blob-1 {
            top: 25%;
            left: 25%;
            width: 16rem;
            height: 16rem;
        }
        
        .blob-2 {
            bottom: 33%;
            right: 33%;
            width: 20rem;
            height: 20rem;
        }
        
        .hero-content {
            text-align: center;
            max-width: 56rem;
            margin: 0 auto;
            position: relative;
            z-index: 10;
        }
        
        .hero-title {
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            font-size: 3.65rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
        }
        
        .hero-name {
            color: transparent;
            background-clip: text;
            background-image: linear-gradient(to right, white, #d1d5db);
        }
        
        .hero-subtitle {
            font-size: 1.75rem;
            font-weight: 500;
            margin-bottom: 2rem;
        }
        
        .typing-text {
            display: inline-block;
        }
        
        .hero-description {
            color: #d1d5db;
            margin-bottom: 2.5rem;
            font-size: 1.3rem;
            max-width: 42rem;
            margin-left: auto;
            margin-right: auto;
        }
        
        .hero-buttons {
            display: flex;
            justify-content: center;
            gap: 1rem;
        }
        .hero-button {
            display: flex;
            align-items: center;
            border-radius: 9999px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            font-size: 1.05rem;
            border: none;
            outline: none;
            cursor: pointer;
            transition: transform 0.18s cubic-bezier(.4,0,.2,1), box-shadow 0.18s cubic-bezier(.4,0,.2,1), background 0.18s, color 0.18s;
            box-shadow: 0 2px 8px 0 rgba(0,0,0,0.10);
            text-decoration: none;
        }
        .hero-button .button-icon {
            margin-right: 0.5rem;
        }
        .glass-button {
            background: rgba(0,0,0,0.7);
            color: white;
            border: 1.5px solid rgba(255,255,255,0.488);
        }
        .glass-button:hover {
            background: white;
            color: black;
            transform: translateY(-4px) scale(1.05);
            box-shadow: 0 6px 24px 0 rgba(0,0,0,0.18);
        }
        .primary-button {
            background: rgb(0, 0, 0);
            color: rgb(255, 255, 255);
            border: 1.5px solid rgba(255, 255, 255, 0.488);
        }
        .primary-button:hover {
            background: rgb(255, 255, 255);
            color: rgb(0, 0, 0);
            transform: translateY(-4px) scale(1.05);
            box-shadow: 0 6px 24px 0 rgba(0, 0, 0, 0.18);
        }
        .hero-button:active {
            transform: scale(0.97);
        }
        .hero-button, .hero-button:visited, .hero-button:focus {
            text-decoration: none;
        }
        .hero-scroll {
            position: absolute;
            bottom: 2.5rem;
            left: 50%;
            transform: translateX(-50%);
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateX(-50%) translateY(0);
            }
            40% {
                transform: translateX(-50%) translateY(-30px);
            }
            60% {
                transform: translateX(-50%) translateY(-15px);
            }
        }
        
        .scroll-down {
            color: white;
        }
        
        .scroll-down i {
            font-size: 1.5rem;
        }
        
        .hero-text {
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        .typing-cursor {
            animation: blink 1s step-end infinite;
        }
        @keyframes blink {
            from, to { opacity: 1; }
            50% { opacity: 0; }
        }
/*=================== Social Links ==============================*/ 
ul {
  list-style: none;
  display: flex;
  flex-direction: row;
  gap: 0.5em;
  position: fixed;
  top: 37rem;
  left: -35rem;
  z-index: 1000;
  
}

.example-2 {
  display: flex;
  justify-content: center;
  align-items: center;
}
.example-2 .icon-content {
  margin: 0 10px;
  position: relative;
}
.example-2 .icon-content .tooltip {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
  padding: 6px 10px;
  border-radius: 5px;
  opacity: 0;
  visibility: hidden;
  font-size: 14px;
  transition: all 0.3s ease;
}
.example-2 .icon-content:hover .tooltip {
  opacity: 1;
  visibility: visible;
  top: -50px;
}
.example-2 .icon-content a {
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  color: #4d4d4d;
  background-color: #fff;
  transition: all 0.3s ease-in-out;
}
.example-2 .icon-content a:hover {
  box-shadow: 3px 2px 45px 0px rgb(0 0 0 / 12%);
}
.example-2 .icon-content a svg {
  position: relative;
  z-index: 1;
  width: 30px;
  height: 30px;
}
.example-2 .icon-content a:hover {
  color: white;
}
.example-2 .icon-content a .filled {
  position: absolute;
  top: auto;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0;
  background-color: #000;
  transition: all 0.3s ease-in-out;
}
.example-2 .icon-content a:hover .filled {
  height: 100%;
}

.example-2 .icon-content a[data-social="linkedin"] .filled,
.example-2 .icon-content a[data-social="linkedin"] ~ .tooltip {
  background-color: #0274b3;
}

.example-2 .icon-content a[data-social="github"] .filled,
.example-2 .icon-content a[data-social="github"] ~ .tooltip {
  background-color: #24262a;
}
.example-2 .icon-content a[data-social="instagram"] .filled,
.example-2 .icon-content a[data-social="instagram"] ~ .tooltip {
  background: linear-gradient(
    45deg,
    #405de6,
    #5b51db,
    #b33ab4,
    #c135b4,
    #e1306c,
    #fd1f1f
  );
}
.example-2 .icon-content a[data-social="youtube"] .filled,
.example-2 .icon-content a[data-social="youtube"] ~ .tooltip {
  background-color: #ff0000;
}


/*==================PROJECT-SECTION=========================================================*/

.projects {
  min-height: 100vh;
  background: black;
  position: relative;
  overflow: hidden;
  padding: 80px 0;
}

.project-title{
  display: flex;
  z-index: 10;
  font-size: 3.5rem;
  margin-bottom: 2rem;
  margin-top: -2rem;
  margin-left: 630.5px;
}



@keyframes backgroundFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(1deg); }
}

.project-display {
  max-width: 1400px;
  max-height: 950px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-template-rows: repeat(8, 120px);
  gap: 20px;
  position: relative;
  z-index: 2;
}

/* Project Card Base Styles */
.project-display > a {
  position: relative;
  background: rgba(255, 255, 255, 0.221);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Orbitron', monospace;
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  text-decoration: none;
}

.project-display > a::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
  z-index: 1;
}

.project-display > a:hover::before {
  transform: translateX(100%);
}

.project-display > a:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.15);
  color: white;
}

.project-display > a span {
  position: relative;
  z-index: 2;
}

/* Individual Project Cards */
.div1 {
  grid-column: 1 / 5;
  grid-row: 1 / 5;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  margin-left: -2.3rem;
}



.div2 {
  grid-column: 5 / 9;
  grid-row: 1 / 4;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  width: 450px;
  margin-left: 0rem;
}

.div3 {
  grid-column: 9 / 13;
  grid-row: 1 / 4;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  width: 440px;
  margin-left: -52.5rem;
}

.div4 {
  grid-column: 1 / 4;
  grid-row: 5 / 8;
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  width: 400px;
  margin-left: -2rem;
}

.div5 {
  grid-column: 4 / 8;
  grid-row: 5 / 8;
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  width: 500px;
}

.div6 {
  grid-column: 8 / 13;
  grid-row: 5 / 8;
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  margin-left: -50rem;
  width: 520px;
}

/* Hover Effects for Each Card */
.div1:hover {
  background: linear-gradient(135deg, #7c8ff0 0%, #8a5bb8 100%);
}

.div2:hover {
  background: linear-gradient(135deg, #f5a3c7 0%, #f7798c 100%);
}

.div3:hover {
  background: linear-gradient(135deg, #6fb8fe 0%, #33f3fe 100%);
}

.div4:hover {
  background: linear-gradient(135deg, #5ffa9b 0%, #4afae7 100%);
}

.div5:hover {
  background: linear-gradient(135deg, #fb8ab0 0%, #feea50 100%);
}

.div6:hover {
  background: linear-gradient(135deg, #b8f0ed 0%, #fee6f3 100%);
}

/* Project Info Overlay */
.project-display > a::after {
  content: attr(data-project-title);
  position: absolute;
  bottom: 20px;
  left: 20px;
  font-size: 1.2rem;
  font-weight: 600;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
  z-index: 3;
}

.project-display > a:hover::after {
  opacity: 1;
  transform: translateY(0);
}

/* Tech Stack Tags */
.project-display > a .tech-stack {
  position: absolute;
  top: 20px;
  left: 20px;
  font-size: 0.7rem;
  font-weight: 400;
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.3s ease;
  background: rgba(0, 0, 0, 0.3);
  padding: 4px 8px;
  border-radius: 10px;
  backdrop-filter: blur(10px);
  z-index: 3;
  color: white;
}

.project-display > a:hover .tech-stack {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .project-display {
    grid-template-columns: repeat(8, 1fr);
    grid-template-rows: repeat(10, 100px);
    gap: 15px;
  }
  
  .div1 {
    grid-column: 1 / 5;
    grid-row: 1 / 5;
  }
  
  .div2 {
    grid-column: 5 / 9;
    grid-row: 1 / 4;
  }
  
  .div3 {
    grid-column: 1 / 5;
    grid-row: 5 / 8;
  }
  
  .div4 {
    grid-column: 5 / 9;
    grid-row: 5 / 8;
  }
  
  .div5 {
    grid-column: 1 / 5;
    grid-row: 8 / 11;
  }
  
  .div6 {
    grid-column: 5 / 9;
    grid-row: 8 / 11;
  }
}

@media (max-width: 768px) {
  .project-display {
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(15, 80px);
    gap: 10px;
    padding: 0 15px;
  }
  
  .div1, .div2, .div3, .div4, .div5, .div6 {
    grid-column: 1 / 5;
  }
  
  .div1 { grid-row: 1 / 5; }
  .div2 { grid-row: 5 / 8; }
  .div3 { grid-row: 8 / 11; }
  .div4 { grid-row: 11 / 14; }
  .div5 { grid-row: 14 / 17; }
  .div6 { grid-row: 17 / 20; }
  
  .project-display > a {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .projects {
    padding: 40px 0;
  }
  
  .project-display {
    grid-template-rows: repeat(15, 70px);
    gap: 8px;
  }
  
  .project-display > a {
    font-size: 1rem;
    border-radius: 15px;
  }
}

/* Loading Animation for Projects */
.project-display > a {
  animation: projectFadeIn 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

.div1 { animation-delay: 0.1s; }
.div2 { animation-delay: 0.2s; }
.div3 { animation-delay: 0.3s; }
.div4 { animation-delay: 0.4s; }
.div5 { animation-delay: 0.5s; }
.div6 { animation-delay: 0.6s; }

@keyframes projectFadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Click Animation */
.project-display > a:active {
  transform: translateY(-5px) scale(0.98);
  transition: transform 0.1s ease;
}

/* Hide project title by default, show on hover */
.project-display > a::after {
  display: none;
}
.project-display > a:hover::after {
  display: block;
}

.div1::after {
  display: none !important;
}
.div1:hover::after {
  display: block !important;
}


/* Adjust tech stack position for .div1 */
.div1 .tech-stack {
  top: 54px;
  left: 24px;
  right: auto;
  font-size: 0.8rem;
  background: rgba(60, 60, 80, 0.35);
  color: #fff;
  z-index: 3;
  padding: 5px 12px;
}


/* Projects Section Quote */
.projects-quote {
  align-self: center;
  justify-self: center;
  font-family: 'Rajdhani', 'Segoe UI', sans-serif;
  font-size: 2rem;
  color: #fff;
  background: rgba(62,201,243,0.10);
  border-radius: 18px;
  padding: 18px 32px;
  margin: 0 0 18px 0;
  text-align: center;
  box-shadow: 0 2px 16px 0 rgba(62,201,243,0.08);
  letter-spacing: 0.5px;
  z-index: 3;
  font-weight: 600;
  animation: quoteFadeIn 1.2s cubic-bezier(0.23, 1, 0.32, 1) forwards;
  opacity: 0;
  width: 850px;
  height: 50px;
  display: flex;
}

@keyframes quoteFadeIn {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.97);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* ===== ABOUT SECTION STYLES ===== */
.about-section {
  position: relative;
  min-height: 100vh;
  background: #00000000;
  color: #ffffff;
  padding: 120px 0 80px;
  overflow: hidden;
}

.about-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 1;
}

.about-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 20% 80%, rgba(62, 201, 243, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(102, 16, 242, 0.1) 0%, transparent 50%);
  animation: backgroundFloat 20s ease-in-out infinite;
}

.about-container {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

/* About Header */
.about-header {
  text-align: center;
  margin-bottom: 80px;
}

.about-title {
  font-family: 'Orbitron', monospace;
  font-size: 3.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 20px;
  letter-spacing: 2px;
  text-shadow: 0 0 20px rgba(62, 201, 243, 0.5);
}

.about-subtitle {
  font-family: 'Rajdhani', sans-serif;
  font-size: 1.3rem;
  color: #cccccc;
  letter-spacing: 1px;
  text-transform: uppercase;
}

/* About Content */
.about-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 60px;
  margin-bottom: 80px;
  align-items: start;
}

.about-text {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.about-description {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #e0e0e0;
  margin-bottom: 25px;
  text-align: justify;
}

.about-description:last-child {
  margin-bottom: 0;
}

/* Stats Section */
.about-stats {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.stat-item {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 30px;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.stat-item:hover {
  transform: translateY(-5px);
  border-color: rgba(62, 201, 243, 0.3);
  box-shadow: 0 8px 30px rgba(62, 201, 243, 0.2);
}

.stat-number {
  font-family: 'Orbitron', monospace;
  font-size: 2.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 10px;
  text-shadow: 0 0 10px rgba(62, 201, 243, 0.5);
}

.stat-label {
  font-family: 'Rajdhani', sans-serif;
  font-size: 0.9rem;
  color: #cccccc;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Skills Section */
.skills-section {
  margin-bottom: 80px;
}

.skills-title {
  font-family: 'Orbitron', monospace;
  font-size: 2.5rem;
  font-weight: 700;
  color: #ffffff;
  text-align: center;
  margin-bottom: 50px;
  letter-spacing: 1px;
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.skill-category {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 30px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.skill-category:hover {
  transform: translateY(-5px);
  border-color: rgba(255, 255, 255, 0.368);
  box-shadow: 0 8px 30px rgba(255, 255, 255, 0.2);
}

.category-title {
  font-family: 'Orbitron', monospace;
  font-size: 1.3rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 20px;
  text-align: center;
  letter-spacing: 1px;
}

.skill-items {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
}

.skill-item {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 0.9rem;
  color: #ffffff;
  transition: all 0.3s ease;
  cursor: default;
}

.skill-item:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: #ffffff;
  transform: scale(1.05);
}

/* Experience Section */
.experience-section {
  margin-bottom: 80px;
}

.experience-title {
  font-family: 'Orbitron', monospace;
  font-size: 2.5rem;
  font-weight: 700;
  color: #ffffff;
  text-align: center;
  margin-bottom: 50px;
  letter-spacing: 1px;
}

.timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, transparent, #ffffff, transparent);
  transform: translateX(-50%);
}

.timeline-item {
  position: relative;
  margin-bottom: 50px;
  display: flex;
  align-items: center;
}

.timeline-item:nth-child(odd) {
  flex-direction: row;
}

.timeline-item:nth-child(even) {
  flex-direction: row-reverse;
}

.timeline-dot {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 16px;
  height: 16px;
  background: #ffffff;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.601);
  z-index: 2;
}

.timeline-content {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 25px;
  width: 45%;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.timeline-item:nth-child(odd) .timeline-content {
  margin-right: 55%;
}

.timeline-item:nth-child(even) .timeline-content {
  margin-left: 55%;
}

.timeline-content:hover {
  transform: translateY(-3px);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 30px rgba(255, 255, 255, 0.2);
}

.timeline-date {
  font-family: 'Orbitron', monospace;
  font-size: 0.9rem;
  color: #ffffff;
  margin-bottom: 8px;
  font-weight: 600;
}

.timeline-role {
  font-family: 'Rajdhani', sans-serif;
  font-size: 1.2rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 5px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.timeline-company {
  font-size: 1rem;
  color: #cccccc;
  margin-bottom: 10px;
  font-style: italic;
}

.timeline-description {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #e0e0e0;
}

/* CTA Section */
.cta-section {
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  padding: 60px 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.cta-title {
  font-family: 'Orbitron', monospace;
  font-size: 2.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 20px;
  letter-spacing: 1px;
}

.cta-description {
  font-size: 1.1rem;
  color: #cccccc;
  margin-bottom: 40px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-button {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 15px 30px;
  border-radius: 25px;
  text-decoration: none;
  font-family: 'Rajdhani', sans-serif;
  font-weight: 600;
  font-size: 1rem;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.cta-button.primary-button {
  background: linear-gradient(90deg, #c1c1c1 0%, #a2a2a2 100%);
  color: #fff;
  box-shadow: 0 4px 24px 0 rgba(62, 201, 243, 0.25), 0 0 0 0 rgba(102, 16, 242, 0.15);
  border: none;
  transition: 
    background 0.4s cubic-bezier(0.4,0,0.2,1),
    box-shadow 0.3s cubic-bezier(0.4,0,0.2,1),
    transform 0.2s cubic-bezier(0.4,0,0.2,1);
}
.cta-button.primary-button:hover, .cta-button.primary-button:focus {
  background: black;
  box-shadow: 0 6px 32px 0 rgba(62, 201, 243, 0.35), 0 0 16px 2px #6610f2;
  transform: scale(1.05);
  outline: none;
}
.cta-button.primary-button:active {
  transform: scale(0.97);
  box-shadow: 0 2px 8px 0 rgba(62, 201, 243, 0.18);
}

.cta-button.glass-button {
  background: rgba(255,255,255,0.08);
  color: #fff;
  border: 1.5px solid rgba(255,255,255,0.18);
  box-shadow: 0 2px 12px 0 rgba(255,255,255,0.08);
  backdrop-filter: blur(8px);
  transition: 
    background 0.3s cubic-bezier(0.4,0,0.2,1),
    border 0.3s cubic-bezier(0.4,0,0.2,1),
    box-shadow 0.3s cubic-bezier(0.4,0,0.2,1),
    transform 0.2s cubic-bezier(0.4,0,0.2,1);
}
.cta-button.glass-button:hover, .cta-button.glass-button:focus {
  background: rgba(255,255,255,0.18);
  border: 1.5px solid #fff;
  box-shadow: 0 4px 24px 0 rgba(255,255,255,0.18);
  color: #fff;
  transform: scale(1.05);
  outline: none;
}
.cta-button.glass-button:active {
  transform: scale(0.97);
  box-shadow: 0 1px 4px 0 rgba(255,255,255,0.10);
}

/* Responsive Design for About Section */
@media (max-width: 1024px) {
  .about-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .about-stats {
    flex-direction: row;
    justify-content: space-around;
  }
  
  .timeline::before {
    left: 30px;
  }
  
  .timeline-item {
    flex-direction: row !important;
  }
  
  .timeline-dot {
    left: 30px;
  }
  
  .timeline-content {
    width: calc(100% - 80px);
    margin-left: 80px !important;
    margin-right: 0 !important;
  }
}

@media (max-width: 768px) {
  .about-section {
    padding: 80px 0 60px;
  }
  
  .about-container {
    padding: 0 20px;
  }
  
  .about-title {
    font-size: 2.5rem;
  }
  
  .about-subtitle {
    font-size: 1.1rem;
  }
  
  .about-text {
    padding: 30px 20px;
  }
  
  .about-description {
    font-size: 1rem;
  }
  
  .skills-grid {
    grid-template-columns: 1fr;
  }
  
  .about-stats {
    flex-direction: column;
  }
  
  .cta-section {
    padding: 40px 20px;
  }
  
  .cta-title {
    font-size: 2rem;
  }
  
  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .cta-button {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .about-title {
    font-size: 2rem;
  }
  
  .skills-title,
  .experience-title,
  .cta-title {
    font-size: 1.8rem;
  }
  
  .timeline-content {
    width: calc(100% - 60px);
    margin-left: 60px !important;
    padding: 20px;
  }
  
  .timeline-dot {
    left: 20px;
  }
  
  .timeline::before {
    left: 20px;
  }
}

/* ===== CONTACT SECTION STYLES ===== */
.contact-section {
  position: relative;
  min-height: 100vh;
  background: #000000;
  color: #ffffff;
  padding: 120px 0 80px;
  overflow: hidden;
}

.contact-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 1;
}

.contact-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.03) 0%, transparent 50%);
  animation: backgroundFloat 20s ease-in-out infinite;
}

.contact-container {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.contact-header {
  text-align: center;
  margin-bottom: 80px;
}

.contact-title {
  font-family: 'Orbitron', monospace;
  font-size: 3.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 20px;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.contact-subtitle {
  font-family: 'Rajdhani', sans-serif;
  font-size: 1.3rem;
  color: #cccccc;
  letter-spacing: 1px;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: start;
}

/* Contact Information Styles */
.contact-info {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 40px;
  height: fit-content;
}

.info-title {
  font-family: 'Orbitron', monospace;
  font-size: 1.8rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 30px;
  text-align: center;
}

.info-items {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  transition: all 0.3s ease;
}

.info-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.info-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #ffffff, #f0f0f0);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000000;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.info-details {
  flex: 1;
}

.info-label {
  font-family: 'Rajdhani', sans-serif;
  font-size: 0.9rem;
  color: #cccccc;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 5px;
}

.info-value {
  font-size: 1.1rem;
  color: #ffffff;
  font-weight: 500;
}

/* Social Links */
.social-links {
  margin-top: 40px;
  text-align: center;
}

.social-title {
  font-family: 'Rajdhani', sans-serif;
  font-size: 1.2rem;
  color: #ffffff;
  margin-bottom: 20px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.social-icons {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.social-icon {
  width: 45px;
  height: 45px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 1.2rem;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-icon:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-3px);
  color: #000000;
}

/* Contact Form Styles */
.contact-form-container {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 40px;
}

.form-title {
  font-family: 'Orbitron', monospace;
  font-size: 1.8rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 30px;
  text-align: center;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-family: 'Rajdhani', sans-serif;
  font-size: 0.95rem;
  color: #cccccc;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-input,
.form-textarea {
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: #ffffff;
  font-size: 1rem;
  font-family: inherit;
  transition: all 0.3s ease;
  resize: vertical;
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.form-textarea {
  min-height: 120px;
  line-height: 1.6;
}

.submit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 18px 30px;
  background: linear-gradient(135deg, #ffffff, #f0f0f0);
  color: #000000;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  font-family: 'Rajdhani', sans-serif;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.submit-button:hover::before {
  left: 100%;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(255, 255, 255, 0.2);
}

.submit-button:active {
  transform: translateY(0);
}

.button-icon {
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.submit-button:hover .button-icon {
  transform: translateX(3px);
}

/* Form Validation Styles */
.form-input:invalid,
.form-textarea:invalid {
  border-color: rgba(255, 100, 100, 0.5);
}

.form-input:valid,
.form-textarea:valid {
  border-color: rgba(100, 255, 100, 0.3);
}

/* Success/Error Messages */
.form-message {
  padding: 15px;
  border-radius: 8px;
  margin-top: 20px;
  font-weight: 500;
  text-align: center;
}

.form-message.success {
  background: rgba(100, 255, 100, 0.1);
  border: 1px solid rgba(100, 255, 100, 0.3);
  color: #90ee90;
}

.form-message.error {
  background: rgba(255, 100, 100, 0.1);
  border: 1px solid rgba(255, 100, 100, 0.3);
  color: #ffb6b6;
}

/* Loading State */
.submit-button.loading {
  pointer-events: none;
  opacity: 0.7;
}

.submit-button.loading .button-text {
  opacity: 0;
}

.submit-button.loading .button-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .contact-content {
    grid-template-columns: 1fr;
    gap: 60px;
  }
  
  .contact-info,
  .contact-form-container {
    padding: 30px;
  }
}

@media (max-width: 768px) {
  .contact-section {
    padding: 100px 0 60px;
  }
  
  .contact-title {
    font-size: 2.5rem;
  }
  
  .contact-subtitle {
    font-size: 1.1rem;
  }
  
  .contact-header {
    margin-bottom: 60px;
  }
  
  .info-item {
    padding: 15px;
    gap: 15px;
  }
  
  .info-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .social-icons {
    gap: 12px;
  }
  
  .social-icon {
    width: 40px;
    height: 40px;
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .contact-title {
    font-size: 2rem;
  }
  
  .contact-subtitle {
    font-size: 1rem;
  }
  
  .info-title,
  .form-title {
    font-size: 1.5rem;
  }
  
  .contact-info,
  .contact-form-container {
    padding: 25px;
  }
  
  .info-item {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
  
  .info-icon {
    margin: 0 auto;
  }
  
  .submit-button {
    padding: 15px 25px;
    font-size: 1rem;
  }
}
           