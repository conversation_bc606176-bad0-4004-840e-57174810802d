<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" aria-label="viewport">
    <title><PERSON><PERSON><PERSON><PERSON>lio</title>
    <link rel="stylesheet" href="style.css">
    <script src="script.js" defer></script>
    <script type="module" src="https://unpkg.com/@splinetool/viewer@1.10.16/build/spline-viewer.js"></script>

    <!-- Three.js and Animation Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/particles.js/2.0.0/particles.min.js"></script>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;600&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- EmailJS SDK -->
    <script src="https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js"></script>
</head>
<body>
  <!-- Welcome Screen -->
  <div class="welcome-screen" id="welcomeScreen">
    <div class="welcome-container">
      <!-- Three.js Canvas -->
      <canvas id="three-canvas"></canvas>

      <!-- Particles Background -->
      <div id="particles-js"></div>

      <!-- Welcome Content -->
      <div class="welcome-content" id="welcomeContent">
        <div class="welcome-title" id="welcomeTitle">
          Welcome to my portfolio
        </div>
        <div class="welcome-subtitle" id="welcomeSubtitle">
          Frontend Developer • Creative Coder • 3D Enthusiast
        </div>
        <div class="tech-showcase" id="techShowcase">
          <div class="tech-item">React</div>
          <div class="tech-item">Three.js</div>
          <div class="tech-item">TypeScript</div>
          <div class="tech-item">Next.js</div>
          <div class="tech-item">GSAP</div>
          <div class="tech-item">TailwindCSS</div>
        </div>
      </div>

      <!-- Loading Animation -->
      <div class="loading-container" id="loadingContainer">
        <div class="loading-text">Initializing Experience...</div>
        <div class="loading-bar">
          <div class="loading-progress" id="loadingProgress"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Portfolio Content -->
    <nav class="nav-container">
        <div class="nav-menu" id="navMenu">
            <span class="subpill-indicator" id="subpill"></span>
            <a href="#home" class="nav-item home-item active">Home</a>
            <a href="#projects" class="nav-item">Projects</a>
            <a href="#about" class="nav-item">About</a>
            <a href="#contact" class="nav-item">Contact</a>
        </div>
<!-- From Uiverse.io by wilsondesouza --> 
<ul class="example-2">
  <li class="icon-content">
    <a
      href="https://www.linkedin.com/in/pratyush-pushkar-685842292?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app "
      aria-label="LinkedIn"
      data-social="linkedin"
    >
      <div class="filled"></div>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        fill="currentColor"
        class="bi bi-linkedin"
        viewBox="0 0 16 16"
        xml:space="preserve"
      >
        <path
          d="M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854zm4.943 12.248V6.169H2.542v7.225zm-1.2-8.212c.837 0 1.358-.554 1.358-1.248-.015-.709-.52-1.248-1.342-1.248S2.4 3.226 2.4 3.934c0 .694.521 1.248 1.327 1.248zm4.908 8.212V9.359c0-.216.016-.432.08-.586.173-.431.568-.878 1.232-.878.869 0 1.216.662 1.216 1.634v3.865h2.401V9.25c0-2.22-1.184-3.252-2.764-3.252-1.274 0-1.845.7-2.165 1.193v.025h-.016l.016-.025V6.169h-2.4c.03.678 0 7.225 0 7.225z"
          fill="currentColor"
        ></path>
      </svg>
    </a>
    <div class="tooltip">LinkedIn</div>
  </li>
  <li class="icon-content">
    <a href="https://www.github.com/pewpew475" aria-label="GitHub" data-social="github">
      <div class="filled"></div>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        fill="currentColor"
        class="bi bi-github"
        viewBox="0 0 16 16"
        xml:space="preserve"
      >
        <path
          d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 *********.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 ********** 1.21 1.87.87 **********-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 *********-.18 1.32-.27 2-.27s1.36.09 2 .27c1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 **********.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 **********.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .***********.38A8.01 8.01 0 0 0 16 8c0-4.42-3.58-8-8-8"
          fill="currentColor"
        ></path>
      </svg>
    </a>
    <div class="tooltip">GitHub</div>
  </li>
  <li class="icon-content">
    <a
      href="https://www.instagram.com/pr_rac1st/"
      aria-label="Instagram"
      data-social="instagram"
    >
      <div class="filled"></div>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        fill="currentColor"
        class="bi bi-instagram"
        viewBox="0 0 16 16"
        xml:space="preserve"
      >
        <path
          d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.9 3.9 0 0 0-1.417.923A3.9 3.9 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.9 3.9 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.9 3.9 0 0 0-.923-1.417A3.9 3.9 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599s.453.546.598.92c.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.5 2.5 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.5 2.5 0 0 1-.92-.598 2.5 2.5 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233s.008-2.388.046-3.231c.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92s.546-.453.92-.598c.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045zm4.988 1.328a.96.96 0 1 0 0 ********** 0 0 0 0-1.92m-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217m0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334"
          fill="currentColor"
        ></path>
      </svg>
    </a>
    <div class="tooltip">Instagram</div>
  </li>
</ul>


    </nav>

    <!-- Hero Section -->
    <!-- Spline Background (fixed, behind all content) -->
    <div class="spline-bg-fixed" style="position:fixed;z-index:-1;top:0;left:0;width:100vw;height:100vh;overflow:hidden;pointer-events:none;">
      <spline-viewer url="https://prod.spline.design/O43mG-9PWObebih6/scene.splinecode" style="width:100vw;height:100vh;min-height:100vh;pointer-events:none;" interaction="false"></spline-viewer>
      <div class="spline-logo-hider"></div>
    </div>

    <section id="home" class="hero-section">
        <!-- Removed hero-background, now handled globally -->
        <div class="hero-content">
            <h1 class="hero-title">
                Hello, I'm <span class="hero-name">Pratyush</span>
            </h1>
            <div class="hero-subtitle">
                <span class="typing-text">Frontend Developer</span>
                <span class="typing-cursor">|</span>
            </div>
            <p class="hero-description">
                I craft beautiful, responsive web experiences with modern technologies. 
                Focused on performance, accessibility, and pixel-perfect designs.
            </p>
            <div class="hero-buttons">
                <a href="#" class="hero-button glass-button">
                    <i class="fas fa-paper-plane button-icon"></i> Get in Touch
                </a>
                <a href="#" class="hero-button primary-button">
                    <i class="fas fa-eye button-icon"></i> View Work
                </a>
            </div>
        </div>

        <div class="hero-scroll">
            <a href="#about" class="scroll-down">
                <i class="fas fa-chevron-down"></i>
            </a>
        </div>
    </section>

    <section id="projects" class="projects">
      <div class="project-background"></div>
      <div class="project-title">My Projects</div>
      <div class="project-display">
        <a href="#" class="div1" data-project-title="E-Commerce Platform" data-tech-stack="React • Node.js • MongoDB">
          <span>1</span>
          <span class="tech-stack">React • Node.js • MongoDB</span>
        </a>
        <a href="#" class="div2" data-project-title="AI Chat Application" data-tech-stack="Next.js • OpenAI • TypeScript">
          <span>2</span>
          <span class="tech-stack">Next.js • OpenAI • TypeScript</span>
        </a>
        
        <div class="projects-quote">Let's make the web a better place—one repo at a time.</div>

        <a href="#" class="div3" data-project-title="3D Portfolio" data-tech-stack="Three.js • GSAP • WebGL">
          <span>3</span>
          <span class="tech-stack">Three.js • GSAP • WebGL</span>
        </a>
        <a href="#" class="div4" data-project-title="Task Management App" data-tech-stack="Vue.js • Firebase • Tailwind">
          <span>4</span>
          <span class="tech-stack">Vue.js • Firebase • Tailwind</span>
        </a>
        <a href="#" class="div5" data-project-title="Weather Dashboard" data-tech-stack="React • API • Chart.js">
          <span>5</span>
          <span class="tech-stack">React • API • Chart.js</span>
        </a>
        <a href="#" class="div6" data-project-title="Social Media Clone" data-tech-stack="Angular • Express • PostgreSQL">
          <span>6</span>
          <span class="tech-stack">Angular • Express • PostgreSQL</span>
        </a>
      </div>
    </section>
    <!----------About-Section----------->
    <section id="about" class="about-section">
        <div class="about-background"></div>
        <div class="about-container">
            <div class="about-header">
                <h2 class="about-title">About Me</h2>
                <div class="about-subtitle">Passionate Developer & Creative Problem Solver</div>
            </div>
            
            <div class="about-content">
                <div class="about-text">
                    <p class="about-description">
                        I'm a dedicated frontend developer with a passion for creating immersive digital experiences. 
                        With expertise in modern web technologies, I specialize in building responsive, accessible, 
                        and performant applications that delight users and drive business value.
                    </p>
                    
                    <p class="about-description">
                        My journey in web development started with curiosity and has evolved into a deep understanding 
                        of user experience, design principles, and cutting-edge technologies. I believe in writing clean, 
                        maintainable code and staying up-to-date with industry best practices.
                    </p>
                </div>
                
                <div class="about-stats">
                    <div class="stat-item">
                        <div class="stat-number">3+</div>
                        <div class="stat-label">Years Experience</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">50+</div>
                        <div class="stat-label">Projects Completed</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">Client Satisfaction</div>
                    </div>
                </div>
            </div>
            
            <div class="skills-section">
                <h3 class="skills-title">Technical Skills</h3>
                <div class="skills-grid">
                    <div class="skill-category">
                        <h4 class="category-title">Frontend</h4>
                        <div class="skill-items">
                            <span class="skill-item">React.js</span>
                            <span class="skill-item">Next.js</span>
                            <span class="skill-item">TypeScript</span>
                            <span class="skill-item">JavaScript (ES6+)</span>
                            <span class="skill-item">HTML5 & CSS3</span>
                            <span class="skill-item">TailwindCSS</span>
                        </div>
                    </div>
                    
                    <div class="skill-category">
                        <h4 class="category-title">3D & Animation</h4>
                        <div class="skill-items">
                            <span class="skill-item">Three.js</span>
                            <span class="skill-item">WebGL</span>
                            <span class="skill-item">GSAP</span>
                            <span class="skill-item">Canvas API</span>
                            <span class="skill-item">Spline</span>
                        </div>
                    </div>
                    
                    <div class="skill-category">
                        <h4 class="category-title">Tools & Others</h4>
                        <div class="skill-items">
                            <span class="skill-item">Git & GitHub</span>
                            <span class="skill-item">VS Code</span>
                            <span class="skill-item">Figma</span>
                            <span class="skill-item">Responsive Design</span>
                            <span class="skill-item">Performance Optimization</span>
                        </div>
                    </div>
                </div>
            </div>
            
        <!--       <div class="experience-section">
                <h3 class="experience-title">Achievements</h3>
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="timeline-date">2024 - December</div>
                            <div class="timeline-role">Completed multiple </div>
                            <div class="timeline-company"></div>
                            <div class="timeline-description">
                                Leading frontend development for enterprise applications, mentoring junior developers, 
                                and implementing best practices for scalable code architecture.
                            </div>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="timeline-date">2021 - 2023</div>
                            <div class="timeline-role">Frontend Developer</div>
                            <div class="timeline-company">Digital Solutions Co.</div>
                            <div class="timeline-description">
                                Built responsive web applications using React and modern JavaScript, 
                                collaborated with design teams to implement pixel-perfect interfaces.
                            </div>
                        </div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="timeline-date">2020 - 2021</div>
                            <div class="timeline-role">Junior Developer</div>
                            <div class="timeline-company">Startup Ventures</div>
                            <div class="timeline-description">
                                Started my journey in web development, learning modern frameworks 
                                and contributing to various client projects.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="cta-section">
                <h3 class="cta-title">Ready to Work Together?</h3>
                <p class="cta-description">
                    Let's discuss your project and bring your ideas to life with cutting-edge web technologies.
                </p>
                <div class="cta-buttons">
                    <a href="#contact" class="cta-button primary-button">
                        <i class="fas fa-paper-plane"></i> Start a Project
                    </a>
                    <a href="#" class="cta-button glass-button" onclick="downloadResume()">
                        <i class="fas fa-download"></i> Download Resume
                    </a>
                </div>
            </div>
        </div>
    </section>-->
    
    <!-- Contact Section -->
    <section id="contact" class="contact-section">
        <div class="contact-background"></div>
        <div class="contact-container">
            <div class="contact-header">
                <h2 class="contact-title">Get In Touch</h2>
                <div class="contact-subtitle">Let's discuss your next project</div>
            </div>
            
            <div class="contact-content">
                <!-- Contact Information -->
                <div class="contact-info">
                    <h3 class="info-title">Contact Information</h3>
                    <div class="info-items">
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="info-details">
                                <div class="info-label">Email</div>
                                <div class="info-value"><EMAIL></div>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="info-details">
                                <div class="info-label">Phone</div>
                                <div class="info-value">+(91) 9821850868</div>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fab fa-whatsapp"></i>
                            </div>
                            <div class="info-details">
                                <div class="info-label">WhatsApp</div>
                                <div class="info-value">+(91) 9142693970</div>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="info-details">
                                <div class="info-label">Location</div>
                                <div class="info-value">India, SRM University Sonipat Haryana</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="social-links">
                        <h4 class="social-title">Follow Me</h4>
                        <div class="social-icons">
                            <a href="https://www.linkedin.com/in/pratyush-pushkar-685842292?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app " class="social-icon" aria-label="LinkedIn">
                                <i class="fab fa-linkedin"></i>
                            </a>
                            <a href="https://github.com/pewpew475" target="_blank" class="social-icon" aria-label="GitHub">
                                <i class="fab fa-github"></i>
                            </a>
                            <a href="#" class="social-icon" aria-label="Twitter">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="https://www.instagram.com/pr_rac1st/" target="_blank" class="social-icon" aria-label="Instagram">
                                <i class="fab fa-instagram"></i>
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Contact Form -->
                <div class="contact-form-container">
                    <h3 class="form-title">Send Message</h3>
                    <form class="contact-form" id="contactForm">
                        <div class="form-group">
                            <label for="name" class="form-label">Full Name *</label>
                            <input type="text" id="name" name="name" class="form-input" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" id="email" name="email" class="form-input" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="whatsapp" class="form-label">WhatsApp Number (with country code) *</label>
                            <input type="tel" id="whatsapp" name="whatsapp" class="form-input" placeholder="+****************" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="message" class="form-label">Message *</label>
                            <textarea id="message" name="message" class="form-textarea" rows="5" required></textarea>
                        </div>
                        
                        <button type="submit" class="submit-button">
                            <span class="button-text">Send Message</span>
                            <span class="button-icon">
                                <i class="fas fa-paper-plane"></i>
                            </span>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>
    
</body>
</html>